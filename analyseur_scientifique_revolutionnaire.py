#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
ANALYSEUR SCIENTIFIQUE RÉVOLUTIONNAIRE - SYSTÈME COMPLEXE BACCARAT LUPASCO
=========================================================================

MISSION CRITIQUE : Démonstration mathématique rigoureuse que le baccarat n'est pas
un jeu de hasard pur mais un système complexe organisé avec :

🎯 OBJECTIFS DE QUANTIFICATION :
1. Mémoire systémique : Le jeu "se souvient" et s'auto-régule
2. Corrélations séquentielles : Les mains ne sont pas indépendantes  
3. Mécanismes d'équilibrage : Tendance vers l'homéostasie
4. Prédictibilité partielle : Patterns détectables et exploitables
5. Système complexe adaptatif : Émergence d'ordre à partir du chaos apparent

🔬 PREUVES À ÉTABLIR :
- Équilibre SYNC/DESYNC impossible en hasard pur (p-value < 10^-8)
- Constance universelle de cet équilibre dans toutes les sous-catégories
- Mécanismes de compensation entre catégories A/B/C
- Structure fractale reproduite à tous les niveaux
- Entropie contrôlée maintenant ordre et désordre simultanément

Configuration cognitive révolutionnaire activée.
"""

import json
import numpy as np
import pandas as pd
import scipy.stats as stats
from scipy import signal
from scipy.stats import binom, chi2_contingency, kstest
import matplotlib.pyplot as plt
import seaborn as sns
from typing import List, Dict, Tuple, Any
from collections import Counter, defaultdict
import warnings
warnings.filterwarnings('ignore')

class AnalyseurScientifiqueRevolutionnaire:
    """
    Analyseur scientifique révolutionnaire pour la détection de structures
    cachées dans le système complexe baccarat Lupasco.
    """
    
    def __init__(self, dataset_path: str):
        """Initialise l'analyseur avec le dataset"""
        self.dataset_path = dataset_path
        self.data = None
        self.sequences = {}
        self.resultats = {}
        
        print("🔬 ANALYSEUR SCIENTIFIQUE RÉVOLUTIONNAIRE INITIALISÉ")
        print("🎯 MISSION : QUANTIFIER LE SYSTÈME COMPLEXE BACCARAT")
        print("=" * 70)
    
    def charger_dataset(self) -> bool:
        """Charge et valide le dataset"""
        try:
            with open(self.dataset_path, 'r', encoding='utf-8') as f:
                self.data = json.load(f)
            
            print(f"✅ Dataset chargé : {self.data['metadata']['nombre_parties']} parties")
            return True
            
        except Exception as e:
            print(f"❌ Erreur chargement : {e}")
            return False
    
    def extraire_sequences(self):
        """Extrait toutes les séquences temporelles pour analyse"""
        print("\n🔍 EXTRACTION DES SÉQUENCES TEMPORELLES...")
        
        # Séquences globales
        self.sequences['index1'] = []
        self.sequences['index2'] = []
        self.sequences['index3'] = []
        self.sequences['index5'] = []
        self.sequences['cards_count'] = []
        
        # Métadonnées temporelles
        self.sequences['partie_numbers'] = []
        self.sequences['main_numbers'] = []
        self.sequences['manche_numbers'] = []
        
        total_mains = 0
        mains_valides = 0
        
        for partie in self.data['parties']:
            partie_num = partie['partie_number']
            
            for main in partie['mains']:
                total_mains += 1
                
                # Ignorer les mains dummy
                if main.get('main_number') is None:
                    continue
                
                mains_valides += 1
                
                # Extraire les données
                self.sequences['index1'].append(main['index1'])
                self.sequences['index2'].append(main['index2'])
                self.sequences['index3'].append(main['index3'])
                self.sequences['index5'].append(main['index5'])
                self.sequences['cards_count'].append(main['cards_count'])
                
                # Métadonnées
                self.sequences['partie_numbers'].append(partie_num)
                self.sequences['main_numbers'].append(main['main_number'])
                self.sequences['manche_numbers'].append(main.get('manche_pb_number'))
        
        print(f"✅ Séquences extraites :")
        print(f"   • Total mains : {total_mains}")
        print(f"   • Mains valides : {mains_valides}")
        print(f"   • Longueur séquence INDEX1 : {len(self.sequences['index1'])}")
        
        return mains_valides
    
    def analyser_equilibre_sync_desync(self):
        """
        PHASE 1 : Analyse critique de l'équilibre SYNC/DESYNC
        Calcul de la p-value exacte pour prouver l'impossibilité statistique
        """
        print("\n🚨 PHASE 1 : QUANTIFICATION ÉQUILIBRE SYNC/DESYNC")
        print("=" * 60)
        
        seq_index1 = np.array(self.sequences['index1'])
        n_total = len(seq_index1)
        n_sync = np.sum(seq_index1 == 0)
        n_desync = np.sum(seq_index1 == 1)
        
        # Proportions observées
        p_sync = n_sync / n_total
        p_desync = n_desync / n_total
        ecart = abs(p_sync - p_desync)
        
        print(f"📊 OBSERVATIONS :")
        print(f"   • SYNC (0) : {n_sync} ({p_sync:.6f})")
        print(f"   • DESYNC (1) : {n_desync} ({p_desync:.6f})")
        print(f"   • Écart : {ecart:.6f} ({ecart*100:.4f}%)")
        
        # Test binomial exact - Probabilité d'observer un écart ≤ observé
        # sous H0 : p = 0.5 (hasard pur)
        p_theorique = 0.5
        
        # Calcul de la p-value bilatérale exacte
        # P(|X - np| ≥ |obs - np|) où X ~ Binomial(n, 0.5)
        esperance = n_total * p_theorique
        ecart_observe = abs(n_sync - esperance)
        
        # P-value exacte bilatérale
        p_value = 2 * binom.sf(esperance + ecart_observe - 1, n_total, p_theorique)
        
        print(f"\n🔬 ANALYSE STATISTIQUE CRITIQUE :")
        print(f"   • H0 : Hasard pur (p = 0.5)")
        print(f"   • Écart observé : {ecart_observe:.1f} mains")
        print(f"   • P-value exacte : {p_value:.2e}")
        
        if p_value < 1e-8:
            print(f"   🚨 CONCLUSION : REJET CATÉGORIQUE DE H0")
            print(f"   🎯 PREUVE : Équilibre impossible en hasard pur")
        
        # Stockage des résultats
        self.resultats['equilibre_sync_desync'] = {
            'n_total': n_total,
            'n_sync': n_sync,
            'n_desync': n_desync,
            'p_sync': p_sync,
            'p_desync': p_desync,
            'ecart': ecart,
            'p_value': p_value,
            'rejet_hasard_pur': p_value < 1e-8
        }
        
        return p_value
    
    def analyser_correlations_sequentielles(self):
        """
        PHASE 2 : Détection de la mémoire systémique
        Calcul des autocorrélations pour prouver la non-indépendance
        """
        print("\n🚨 PHASE 2 : QUANTIFICATION MÉMOIRE SYSTÉMIQUE")
        print("=" * 60)
        
        seq_index1 = np.array(self.sequences['index1'])
        n = len(seq_index1)
        
        # Calcul des autocorrélations pour différents lags
        lags = [1, 2, 3, 5, 10, 20, 50, 100]
        autocorrelations = {}
        
        print(f"🔍 CALCUL AUTOCORRÉLATIONS (n={n}) :")
        
        for lag in lags:
            if lag < n:
                # Autocorrélation de Pearson
                x1 = seq_index1[:-lag]
                x2 = seq_index1[lag:]
                
                if len(x1) > 0:
                    corr = np.corrcoef(x1, x2)[0, 1]
                    autocorrelations[lag] = corr
                    
                    # Test de significativité
                    # Sous H0 : indépendance, corr ~ N(0, 1/sqrt(n))
                    std_err = 1 / np.sqrt(len(x1))
                    z_score = corr / std_err
                    p_val = 2 * (1 - stats.norm.cdf(abs(z_score)))
                    
                    significatif = p_val < 0.05
                    symbole = "🔥" if significatif else "  "
                    
                    print(f"   • Lag {lag:3d} : r = {corr:+.6f} | p = {p_val:.4f} {symbole}")
        
        # Détection de patterns cycliques
        print(f"\n🔍 DÉTECTION DE CYCLES :")
        
        # Analyse spectrale (FFT)
        fft = np.fft.fft(seq_index1 - np.mean(seq_index1))
        freqs = np.fft.fftfreq(len(seq_index1))
        power = np.abs(fft)**2
        
        # Trouver les fréquences dominantes
        idx_max = np.argsort(power)[-10:]  # Top 10 fréquences
        
        cycles_detectes = []
        for idx in idx_max:
            if freqs[idx] > 0:  # Fréquences positives seulement
                periode = 1 / freqs[idx]
                if 2 <= periode <= n/4:  # Périodes raisonnables
                    cycles_detectes.append((periode, power[idx]))
        
        cycles_detectes.sort(key=lambda x: x[1], reverse=True)
        
        for i, (periode, puissance) in enumerate(cycles_detectes[:5]):
            print(f"   • Cycle {i+1} : période ≈ {periode:.1f} mains (puissance: {puissance:.0f})")
        
        # Stockage des résultats
        self.resultats['correlations_sequentielles'] = {
            'autocorrelations': autocorrelations,
            'cycles_detectes': cycles_detectes,
            'memoire_detectee': any(abs(corr) > 2/np.sqrt(n) for corr in autocorrelations.values())
        }
        
        return autocorrelations
    
    def analyser_mecanismes_compensation(self):
        """
        PHASE 3 : Quantification des mécanismes de compensation A/B/C
        """
        print("\n🚨 PHASE 3 : MÉCANISMES DE COMPENSATION A/B/C")
        print("=" * 60)
        
        # Compter les distributions par catégorie
        seq_index2 = self.sequences['index2']
        seq_index3 = self.sequences['index3']
        seq_index1 = self.sequences['index1']
        
        # Distribution INDEX2 (A/B/C)
        count_index2 = Counter(seq_index2)
        total = len(seq_index2)
        
        print(f"📊 DISTRIBUTION INDEX2 (CARTES) :")
        for cat in ['A', 'B', 'C']:
            count = count_index2[cat]
            pct = count / total * 100
            print(f"   • {cat} : {count} ({pct:.2f}%)")
        
        # Analyse croisée INDEX2 × INDEX3
        print(f"\n📊 ANALYSE CROISÉE INDEX2 × INDEX3 :")
        
        compensation_matrix = defaultdict(lambda: defaultdict(int))
        
        for i2, i3 in zip(seq_index2, seq_index3):
            compensation_matrix[i2][i3] += 1
        
        # Calculer les asymétries BANKER/PLAYER par catégorie
        asymetries = {}
        
        for cat in ['A', 'B', 'C']:
            banker = compensation_matrix[cat]['BANKER']
            player = compensation_matrix[cat]['PLAYER']
            tie = compensation_matrix[cat]['TIE']
            total_cat = banker + player + tie
            
            if total_cat > 0:
                pct_banker = banker / total_cat * 100
                pct_player = player / total_cat * 100
                pct_tie = tie / total_cat * 100
                asymetrie = pct_banker - pct_player
                
                asymetries[cat] = asymetrie
                
                print(f"   • {cat} : BANKER {pct_banker:.1f}% | PLAYER {pct_player:.1f}% | TIE {pct_tie:.1f}%")
                print(f"     → Asymétrie B-P : {asymetrie:+.1f}%")
        
        # Vérifier le mécanisme de compensation global
        print(f"\n🎯 MÉCANISME DE COMPENSATION DÉTECTÉ :")
        
        total_banker = sum(compensation_matrix[cat]['BANKER'] for cat in ['A', 'B', 'C'])
        total_player = sum(compensation_matrix[cat]['PLAYER'] for cat in ['A', 'B', 'C'])
        total_bp = total_banker + total_player
        
        equilibre_global = abs(total_banker - total_player) / total_bp * 100
        
        print(f"   • Équilibre global B/P : {equilibre_global:.3f}% d'écart")
        print(f"   • Compensation A/B/C : {asymetries}")
        
        # Stockage des résultats
        self.resultats['mecanismes_compensation'] = {
            'distribution_index2': dict(count_index2),
            'asymetries_par_categorie': asymetries,
            'equilibre_global_bp': equilibre_global,
            'compensation_detectee': equilibre_global < 2.0  # Seuil arbitraire
        }
        
        return asymetries

    def analyser_structure_fractale(self):
        """
        PHASE 4 : Détection de structures fractales et auto-similarité
        """
        print("\n🚨 PHASE 4 : STRUCTURES FRACTALES ET AUTO-SIMILARITÉ")
        print("=" * 60)

        seq_index5 = self.sequences['index5']
        n = len(seq_index5)

        # Analyser la distribution à différentes échelles
        echelles = [10, 50, 100, 500, 1000]
        distributions_echelles = {}

        print(f"🔍 ANALYSE MULTI-ÉCHELLE (n={n}) :")

        for echelle in echelles:
            if echelle <= n:
                # Découper la séquence en blocs de taille 'echelle'
                nb_blocs = n // echelle
                distributions_blocs = []

                for i in range(nb_blocs):
                    debut = i * echelle
                    fin = debut + echelle
                    bloc = seq_index5[debut:fin]

                    # Distribution INDEX5 dans ce bloc
                    count_bloc = Counter(bloc)
                    total_bloc = len(bloc)

                    # Convertir en proportions
                    prop_bloc = {k: v/total_bloc for k, v in count_bloc.items()}
                    distributions_blocs.append(prop_bloc)

                distributions_echelles[echelle] = distributions_blocs

                # Calculer la variance inter-blocs pour chaque INDEX5
                index5_uniques = set(seq_index5)
                variances = {}

                for idx5 in index5_uniques:
                    props = [bloc.get(idx5, 0) for bloc in distributions_blocs]
                    if len(props) > 1:
                        variances[idx5] = np.var(props)

                variance_moyenne = np.mean(list(variances.values())) if variances else 0

                print(f"   • Échelle {echelle:4d} : {nb_blocs} blocs | Variance moy. = {variance_moyenne:.6f}")

        # Test d'auto-similarité : comparer distributions globale vs locales
        print(f"\n🔍 TEST D'AUTO-SIMILARITÉ :")

        # Distribution globale
        count_global = Counter(seq_index5)
        total_global = len(seq_index5)
        prop_global = {k: v/total_global for k, v in count_global.items()}

        # Comparer avec distributions locales (échelle 100)
        if 100 in distributions_echelles:
            blocs_100 = distributions_echelles[100]

            # Calculer la distance KL moyenne entre global et local
            distances_kl = []

            for bloc in blocs_100:
                # Distance KL : D_KL(P||Q) = Σ P(x) log(P(x)/Q(x))
                kl_div = 0
                for idx5 in prop_global:
                    p = bloc.get(idx5, 1e-10)  # Éviter log(0)
                    q = prop_global[idx5]
                    if p > 0 and q > 0:
                        kl_div += p * np.log(p / q)

                distances_kl.append(kl_div)

            kl_moyenne = np.mean(distances_kl)
            kl_std = np.std(distances_kl)

            print(f"   • Distance KL moyenne : {kl_moyenne:.6f} ± {kl_std:.6f}")
            print(f"   • Auto-similarité : {'FORTE' if kl_moyenne < 0.1 else 'MODÉRÉE' if kl_moyenne < 0.5 else 'FAIBLE'}")

        # Stockage des résultats
        self.resultats['structure_fractale'] = {
            'distributions_echelles': distributions_echelles,
            'auto_similarite_detectee': kl_moyenne < 0.1 if 'kl_moyenne' in locals() else False,
            'distance_kl_moyenne': kl_moyenne if 'kl_moyenne' in locals() else None
        }

        return distributions_echelles

    def analyser_entropie_controlee(self):
        """
        PHASE 5 : Quantification de l'entropie contrôlée
        """
        print("\n🚨 PHASE 5 : ENTROPIE CONTRÔLÉE")
        print("=" * 60)

        # Entropie INDEX1 (SYNC/DESYNC)
        seq_index1 = self.sequences['index1']
        count_index1 = Counter(seq_index1)
        total = len(seq_index1)

        # Entropie de Shannon : H = -Σ p(x) log2(p(x))
        entropie_index1 = 0
        for count in count_index1.values():
            p = count / total
            if p > 0:
                entropie_index1 -= p * np.log2(p)

        entropie_max_index1 = np.log2(len(count_index1))  # log2(2) = 1
        efficacite_index1 = entropie_index1 / entropie_max_index1

        print(f"📊 ENTROPIE INDEX1 (SYNC/DESYNC) :")
        print(f"   • Entropie observée : {entropie_index1:.6f} bits")
        print(f"   • Entropie maximale : {entropie_max_index1:.6f} bits")
        print(f"   • Efficacité : {efficacite_index1:.4f} ({efficacite_index1*100:.2f}%)")

        # Entropie INDEX2 (A/B/C)
        seq_index2 = self.sequences['index2']
        count_index2 = Counter(seq_index2)

        entropie_index2 = 0
        for count in count_index2.values():
            p = count / total
            if p > 0:
                entropie_index2 -= p * np.log2(p)

        entropie_max_index2 = np.log2(len(count_index2))  # log2(3) ≈ 1.585
        efficacite_index2 = entropie_index2 / entropie_max_index2

        print(f"\n📊 ENTROPIE INDEX2 (A/B/C) :")
        print(f"   • Entropie observée : {entropie_index2:.6f} bits")
        print(f"   • Entropie maximale : {entropie_max_index2:.6f} bits")
        print(f"   • Efficacité : {efficacite_index2:.4f} ({efficacite_index2*100:.2f}%)")

        # Entropie INDEX5 (18 valeurs)
        seq_index5 = self.sequences['index5']
        count_index5 = Counter(seq_index5)

        entropie_index5 = 0
        for count in count_index5.values():
            p = count / total
            if p > 0:
                entropie_index5 -= p * np.log2(p)

        entropie_max_index5 = np.log2(len(count_index5))
        efficacite_index5 = entropie_index5 / entropie_max_index5

        print(f"\n📊 ENTROPIE INDEX5 (18 VALEURS) :")
        print(f"   • Entropie observée : {entropie_index5:.6f} bits")
        print(f"   • Entropie maximale : {entropie_max_index5:.6f} bits")
        print(f"   • Efficacité : {efficacite_index5:.4f} ({efficacite_index5*100:.2f}%)")

        # Entropie conditionnelle H(INDEX1_{n+1} | INDEX1_n)
        print(f"\n🔍 ENTROPIE CONDITIONNELLE (MÉMOIRE) :")

        # Construire la matrice de transition INDEX1
        transitions = defaultdict(lambda: defaultdict(int))

        for i in range(len(seq_index1) - 1):
            etat_actuel = seq_index1[i]
            etat_suivant = seq_index1[i + 1]
            transitions[etat_actuel][etat_suivant] += 1

        # Calculer H(X_{n+1} | X_n)
        entropie_conditionnelle = 0
        total_transitions = len(seq_index1) - 1

        for etat_actuel in transitions:
            # Probabilité de l'état actuel
            p_actuel = sum(transitions[etat_actuel].values()) / total_transitions

            # Entropie conditionnelle pour cet état
            h_conditionnel = 0
            total_depuis_etat = sum(transitions[etat_actuel].values())

            for count in transitions[etat_actuel].values():
                p_transition = count / total_depuis_etat
                if p_transition > 0:
                    h_conditionnel -= p_transition * np.log2(p_transition)

            entropie_conditionnelle += p_actuel * h_conditionnel

        # Information mutuelle I(X_n; X_{n+1}) = H(X) - H(X|Y)
        info_mutuelle = entropie_index1 - entropie_conditionnelle

        print(f"   • H(INDEX1_{{n+1}} | INDEX1_n) : {entropie_conditionnelle:.6f} bits")
        print(f"   • Information mutuelle : {info_mutuelle:.6f} bits")
        print(f"   • Mémoire détectée : {'OUI' if info_mutuelle > 0.001 else 'NON'}")

        # Stockage des résultats
        self.resultats['entropie_controlee'] = {
            'entropie_index1': entropie_index1,
            'efficacite_index1': efficacite_index1,
            'entropie_index2': entropie_index2,
            'efficacite_index2': efficacite_index2,
            'entropie_index5': entropie_index5,
            'efficacite_index5': efficacite_index5,
            'entropie_conditionnelle': entropie_conditionnelle,
            'information_mutuelle': info_mutuelle,
            'memoire_detectee': info_mutuelle > 0.001
        }

        return {
            'entropie_index1': entropie_index1,
            'efficacite_index1': efficacite_index1,
            'information_mutuelle': info_mutuelle
        }

    def generer_rapport_revolutionnaire(self):
        """
        Génère le rapport scientifique révolutionnaire complet
        """
        print("\n" + "="*80)
        print("🔬 RAPPORT SCIENTIFIQUE RÉVOLUTIONNAIRE - SYSTÈME COMPLEXE BACCARAT")
        print("="*80)

        print(f"\n🎯 MISSION ACCOMPLIE : QUANTIFICATION DU SYSTÈME COMPLEXE")
        print(f"📊 Dataset analysé : {len(self.sequences['index1'])} mains valides")

        # Résumé des découvertes
        print(f"\n🚨 DÉCOUVERTES RÉVOLUTIONNAIRES :")

        # 1. Équilibre SYNC/DESYNC
        eq = self.resultats['equilibre_sync_desync']
        print(f"\n1️⃣ ÉQUILIBRE SYNC/DESYNC IMPOSSIBLE EN HASARD PUR")
        print(f"   • Écart observé : {eq['ecart']*100:.4f}%")
        print(f"   • P-value : {eq['p_value']:.2e}")
        print(f"   • Conclusion : {'REJET CATÉGORIQUE du hasard pur' if eq['rejet_hasard_pur'] else 'Hasard possible'}")

        # 2. Mémoire systémique
        corr = self.resultats['correlations_sequentielles']
        print(f"\n2️⃣ MÉMOIRE SYSTÉMIQUE DÉTECTÉE")
        print(f"   • Autocorrélations significatives : {'OUI' if corr['memoire_detectee'] else 'NON'}")
        print(f"   • Cycles détectés : {len(corr['cycles_detectes'])} patterns")

        # 3. Mécanismes de compensation
        comp = self.resultats['mecanismes_compensation']
        print(f"\n3️⃣ MÉCANISMES DE COMPENSATION A/B/C")
        print(f"   • Équilibre global B/P : {comp['equilibre_global_bp']:.3f}%")
        print(f"   • Compensation active : {'OUI' if comp['compensation_detectee'] else 'NON'}")

        # 4. Structure fractale
        fract = self.resultats['structure_fractale']
        print(f"\n4️⃣ STRUCTURE FRACTALE")
        print(f"   • Auto-similarité : {'DÉTECTÉE' if fract['auto_similarite_detectee'] else 'NON DÉTECTÉE'}")

        # 5. Entropie contrôlée
        entr = self.resultats['entropie_controlee']
        print(f"\n5️⃣ ENTROPIE CONTRÔLÉE")
        print(f"   • Efficacité INDEX1 : {entr['efficacite_index1']*100:.2f}%")
        print(f"   • Information mutuelle : {entr['information_mutuelle']:.6f} bits")
        print(f"   • Mémoire entropique : {'DÉTECTÉE' if entr['memoire_detectee'] else 'NON DÉTECTÉE'}")

        # Conclusion révolutionnaire
        print(f"\n" + "="*80)
        print(f"🎉 CONCLUSION SCIENTIFIQUE RÉVOLUTIONNAIRE")
        print(f"="*80)

        preuves_convergentes = sum([
            eq['rejet_hasard_pur'],
            corr['memoire_detectee'],
            comp['compensation_detectee'],
            fract['auto_similarite_detectee'],
            entr['memoire_detectee']
        ])

        print(f"🔥 PREUVES CONVERGENTES : {preuves_convergentes}/5")

        if preuves_convergentes >= 3:
            print(f"🚨 VERDICT : LE BACCARAT EST UN SYSTÈME COMPLEXE ORGANISÉ")
            print(f"🎯 IMPLICATIONS : Mémoire, corrélations, prédictibilité partielle")
        else:
            print(f"⚠️ VERDICT : Preuves insuffisantes pour rejeter le hasard")

        return self.resultats

    def executer_analyse_complete(self):
        """Exécute l'analyse scientifique révolutionnaire complète"""

        if not self.charger_dataset():
            return None

        self.extraire_sequences()

        # Exécuter toutes les phases d'analyse
        self.analyser_equilibre_sync_desync()
        self.analyser_correlations_sequentielles()
        self.analyser_mecanismes_compensation()
        self.analyser_structure_fractale()
        self.analyser_entropie_controlee()

        # Générer le rapport final
        return self.generer_rapport_revolutionnaire()


# ============================================================================
# PROGRAMME PRINCIPAL
# ============================================================================

if __name__ == "__main__":
    print("🔬 LANCEMENT ANALYSE SCIENTIFIQUE RÉVOLUTIONNAIRE")
    print("🎯 OBJECTIF : PROUVER QUE LE BACCARAT EST UN SYSTÈME COMPLEXE")
    print("=" * 80)

    # Créer l'analyseur
    analyseur = AnalyseurScientifiqueRevolutionnaire("dataset_baccarat_lupasco_20250701_092454.json")

    # Exécuter l'analyse complète
    resultats = analyseur.executer_analyse_complete()

    if resultats:
        print(f"\n✅ ANALYSE RÉVOLUTIONNAIRE TERMINÉE AVEC SUCCÈS")
        print(f"🔍 Tous les résultats stockés dans l'objet analyseur.resultats")
    else:
        print(f"\n❌ ÉCHEC DE L'ANALYSE")
